"""
Model Inference Module for CELEST AI

This module handles loading trained models and making predictions for
real-time CME detection. It includes model loading from MLflow,
preprocessing of input data, and generation of explainable predictions.

Key components:
- Model loading and caching
- Input data preprocessing and validation
- Prediction generation with confidence scores
- SHAP-based explainability
- Performance monitoring
"""

import pandas as pd
import numpy as np
import torch
import mlflow
import mlflow.pytorch
from sklearn.preprocessing import StandardScaler
import shap
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import pickle
import json
from datetime import datetime, timedelta
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore')


class ModelInference:
    """
    Model inference class for CELEST AI CME detection
    
    Handles model loading, preprocessing, prediction, and explainability
    for real-time CME detection using trained PatchTST models.
    """
    
    def __init__(self, model_uri: Optional[str] = None, scaler_path: Optional[str] = None):
        """
        Initialize the inference engine
        
        Args:
            model_uri: MLflow model URI or local path to model
            scaler_path: Path to the fitted scaler object
        """
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.model_info = {}
        self.explainer = None
        
        # Performance tracking
        self.prediction_count = 0
        self.total_inference_time = 0.0
        
        # Load model and scaler if provided
        if model_uri:
            self.load_model(model_uri)
        if scaler_path:
            self.load_scaler(scaler_path)
    
    def load_model(self, model_uri: str) -> None:
        """
        Load trained model from MLflow or local path
        
        Args:
            model_uri: MLflow model URI (e.g., 'models:/celest-ai/Production') 
                      or local path to model
        """
        logger.info(f"Loading model from {model_uri}")
        
        try:
            if model_uri.startswith('models:/') or model_uri.startswith('runs:/'):
                # Load from MLflow
                self.model = mlflow.pytorch.load_model(model_uri)
                
                # Get model metadata
                client = mlflow.tracking.MlflowClient()
                if model_uri.startswith('models:/'):
                    model_name = model_uri.split('/')[1]
                    model_version = model_uri.split('/')[-1] if len(model_uri.split('/')) > 2 else 'latest'
                    model_version_info = client.get_latest_versions(model_name, stages=["Production"])
                    if model_version_info:
                        run_id = model_version_info[0].run_id
                        run_info = client.get_run(run_id)
                        self.model_info = {
                            'model_name': model_name,
                            'model_version': model_version,
                            'run_id': run_id,
                            'metrics': run_info.data.metrics,
                            'params': run_info.data.params
                        }
                
            else:
                # Load from local path
                self.model = torch.load(model_uri, map_location='cpu')
                self.model_info = {
                    'model_name': 'PatchTST',
                    'model_version': 'local',
                    'loaded_from': model_uri
                }
            
            # Set model to evaluation mode
            self.model.eval()
            
            # Extract feature columns from model if available
            if hasattr(self.model, 'hparams') and 'feature_columns' in self.model.hparams:
                self.feature_columns = self.model.hparams['feature_columns']
            else:
                # Default feature columns
                self.feature_columns = [
                    'Bz_gsm', 'B_total', 'speed', 'density', 'temperature',
                    'dynamic_pressure', 'clock_angle'
                ]
            
            logger.info(f"Model loaded successfully. Features: {self.feature_columns}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def load_scaler(self, scaler_path: str) -> None:
        """
        Load fitted scaler for data preprocessing
        
        Args:
            scaler_path: Path to pickled scaler object
        """
        logger.info(f"Loading scaler from {scaler_path}")
        
        try:
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
            logger.info("Scaler loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load scaler: {e}")
            raise
    
    def preprocess_data(self, data: pd.DataFrame) -> torch.Tensor:
        """
        Preprocess input data for model inference
        
        Args:
            data: DataFrame with solar wind measurements
            
        Returns:
            Preprocessed tensor ready for model input
        """
        # Validate required columns
        missing_cols = [col for col in self.feature_columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Select and order features
        feature_data = data[self.feature_columns].copy()
        
        # Handle missing values
        feature_data = feature_data.fillna(method='ffill').fillna(method='bfill')
        
        # Scale features if scaler is available
        if self.scaler is not None:
            feature_data[self.feature_columns] = self.scaler.transform(feature_data[self.feature_columns])
        
        # Convert to tensor
        tensor_data = torch.FloatTensor(feature_data.values)
        
        # Add batch dimension if needed
        if tensor_data.dim() == 2:
            tensor_data = tensor_data.unsqueeze(0)
        
        return tensor_data
    
    def predict(self, data: pd.DataFrame, return_probabilities: bool = True, 
                explain: bool = False) -> Dict[str, Any]:
        """
        Make prediction on input data
        
        Args:
            data: DataFrame with solar wind measurements
            return_probabilities: Whether to return class probabilities
            explain: Whether to generate SHAP explanations
            
        Returns:
            Dictionary with prediction results
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        start_time = datetime.now()
        
        try:
            # Preprocess data
            input_tensor = self.preprocess_data(data)
            
            # Make prediction
            with torch.no_grad():
                logits = self.model(input_tensor)
                probabilities = torch.softmax(logits, dim=1)
                predicted_class = torch.argmax(logits, dim=1)
            
            # Extract results
            pred_class = predicted_class.item()
            pred_prob = probabilities[0, pred_class].item()
            class_probs = probabilities[0].numpy()
            
            # Determine alert status
            if pred_class == 1:
                if pred_prob > 0.8:
                    status = "ALERT"
                elif pred_prob > 0.6:
                    status = "WATCH"
                else:
                    status = "NORMAL"
            else:
                status = "NORMAL"
            
            # Estimate impact time (simplified heuristic)
            if status in ["ALERT", "WATCH"]:
                # Estimate based on solar wind speed and typical L1-Earth transit time
                latest_speed = data['speed'].iloc[-1] if 'speed' in data.columns else 400
                impact_time = max(30, int(60 - (latest_speed - 400) / 10))  # 30-60 minutes
            else:
                impact_time = 0
            
            # Generate physical drivers explanation
            drivers = self._generate_physical_drivers(data)
            
            # Prepare result
            result = {
                'status': status,
                'confidence': pred_prob,
                'class_probabilities': {
                    'normal': float(class_probs[0]),
                    'cme_event': float(class_probs[1])
                },
                'predicted_impact_time_minutes': impact_time,
                'drivers': drivers,
                'timestamp': datetime.now(),
                'model_version': self.model_info.get('model_version', 'unknown')
            }
            
            # Add SHAP explanations if requested
            if explain:
                try:
                    explanations = self._generate_shap_explanations(input_tensor)
                    result['explanations'] = explanations
                except Exception as e:
                    logger.warning(f"Failed to generate explanations: {e}")
                    result['explanations'] = None
            
            # Update performance tracking
            inference_time = (datetime.now() - start_time).total_seconds()
            self.prediction_count += 1
            self.total_inference_time += inference_time
            
            result['inference_time_seconds'] = inference_time
            
            logger.info(f"Prediction: {status} (confidence: {pred_prob:.3f}, time: {inference_time:.3f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def _generate_physical_drivers(self, data: pd.DataFrame) -> List[str]:
        """
        Generate list of physical drivers based on current conditions
        
        Args:
            data: DataFrame with solar wind measurements
            
        Returns:
            List of driver descriptions
        """
        drivers = []
        
        if len(data) == 0:
            return drivers
        
        # Get latest measurements
        latest = data.iloc[-1]
        
        # Check magnetic field conditions
        if 'Bz_gsm' in latest:
            bz = latest['Bz_gsm']
            if bz < -8:
                drivers.append(f"Strong southward Bz ({bz:.1f} nT)")
            elif bz < -5:
                drivers.append(f"Moderate southward Bz ({bz:.1f} nT)")
        
        if 'B_total' in latest:
            b_total = latest['B_total']
            if b_total > 15:
                drivers.append(f"Enhanced magnetic field ({b_total:.1f} nT)")
            elif b_total > 10:
                drivers.append(f"Elevated magnetic field ({b_total:.1f} nT)")
        
        # Check solar wind conditions
        if 'speed' in latest:
            speed = latest['speed']
            if speed > 600:
                drivers.append(f"High-speed solar wind ({speed:.0f} km/s)")
            elif speed > 500:
                drivers.append(f"Elevated solar wind speed ({speed:.0f} km/s)")
        
        if 'density' in latest:
            density = latest['density']
            if density > 20:
                drivers.append(f"High proton density ({density:.1f} p/cc)")
            elif density > 10:
                drivers.append(f"Elevated proton density ({density:.1f} p/cc)")
        
        # Check derived parameters
        if 'dynamic_pressure' in latest:
            dyn_pressure = latest['dynamic_pressure']
            if dyn_pressure > 10:
                drivers.append(f"High dynamic pressure ({dyn_pressure:.1f} nPa)")
        
        return drivers
    
    def _generate_shap_explanations(self, input_tensor: torch.Tensor) -> Dict[str, Any]:
        """
        Generate SHAP explanations for the prediction
        
        Args:
            input_tensor: Preprocessed input tensor
            
        Returns:
            Dictionary with SHAP values and explanations
        """
        if self.explainer is None:
            # Initialize SHAP explainer (this is computationally expensive)
            logger.info("Initializing SHAP explainer...")
            
            # Create background dataset (use zeros as background)
            background = torch.zeros_like(input_tensor)
            self.explainer = shap.DeepExplainer(self.model, background)
        
        # Calculate SHAP values
        shap_values = self.explainer.shap_values(input_tensor)
        
        # Process SHAP values for interpretation
        if isinstance(shap_values, list):
            # Multi-class case - use positive class
            shap_vals = shap_values[1]
        else:
            shap_vals = shap_values
        
        # Average over sequence dimension to get feature importance
        feature_importance = np.mean(np.abs(shap_vals[0]), axis=0)
        
        # Create feature importance dictionary
        importance_dict = {}
        for i, feature in enumerate(self.feature_columns):
            importance_dict[feature] = float(feature_importance[i])
        
        # Sort by importance
        sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'feature_importance': importance_dict,
            'top_features': sorted_importance[:5],
            'shap_values_shape': shap_vals.shape
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        info = self.model_info.copy()
        info.update({
            'feature_columns': self.feature_columns,
            'prediction_count': self.prediction_count,
            'average_inference_time': self.total_inference_time / max(1, self.prediction_count),
            'scaler_loaded': self.scaler is not None
        })
        return info
    
    def validate_input(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate input data for prediction
        
        Args:
            data: Input DataFrame
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Check if DataFrame is empty
        if len(data) == 0:
            errors.append("Input data is empty")
            return False, errors
        
        # Check required columns
        missing_cols = [col for col in self.feature_columns if col not in data.columns]
        if missing_cols:
            errors.append(f"Missing required columns: {missing_cols}")
        
        # Check for minimum sequence length
        min_length = 180  # 3 hours
        if len(data) < min_length:
            errors.append(f"Insufficient data length: {len(data)} < {min_length}")
        
        # Check for excessive missing values
        for col in self.feature_columns:
            if col in data.columns:
                missing_pct = data[col].isna().mean()
                if missing_pct > 0.5:
                    errors.append(f"Too many missing values in {col}: {missing_pct:.1%}")
        
        # Check for reasonable value ranges
        if 'Bz_gsm' in data.columns:
            if data['Bz_gsm'].abs().max() > 100:
                errors.append("Bz_gsm values outside reasonable range")
        
        if 'speed' in data.columns:
            if (data['speed'] < 100).any() or (data['speed'] > 1500).any():
                errors.append("Solar wind speed values outside reasonable range")
        
        return len(errors) == 0, errors


def main():
    """Example usage of the inference module"""
    
    # Create sample data for testing
    np.random.seed(42)
    n_points = 200
    
    sample_data = pd.DataFrame({
        'timestamp': pd.date_range('2023-01-01', periods=n_points, freq='1min'),
        'Bz_gsm': np.random.normal(-2, 3, n_points),
        'B_total': np.random.normal(8, 2, n_points),
        'speed': np.random.normal(400, 40, n_points),
        'density': np.random.normal(5, 1.5, n_points),
        'temperature': np.random.normal(100000, 15000, n_points),
        'dynamic_pressure': np.random.normal(2, 0.5, n_points),
        'clock_angle': np.random.uniform(-180, 180, n_points)
    })
    
    # Add a CME-like event
    sample_data.loc[150:170, 'Bz_gsm'] = np.random.normal(-8, 1, 21)
    sample_data.loc[150:170, 'speed'] = np.random.normal(550, 20, 21)
    sample_data.loc[150:170, 'density'] = np.random.normal(12, 2, 21)
    
    # Initialize inference engine (without actual model for demo)
    inference = ModelInference()
    
    # Validate input
    is_valid, errors = inference.validate_input(sample_data)
    print(f"Input validation: {'PASS' if is_valid else 'FAIL'}")
    if errors:
        print(f"Errors: {errors}")
    
    print(f"Sample data created with {len(sample_data)} points")
    print(f"Features: {list(sample_data.columns)}")


if __name__ == "__main__":
    main()
